# -*- coding: utf-8 -*-
"""
测试新配置是否能正确爬取目标网站
"""

import requests
from bs4 import BeautifulSoup
from lxml import html
import re
from urllib.parse import urljoin

from config_news_5_2 import (
    START_URL, BASE_URL, NEWS_ITEM_XPATH, NEWS_TITLE_XPATH, NEWS_DATE_XPATH,
    HEADERS, REQUEST_TIMEOUT, TARGET_DATE_RANGE
)

def test_fetch_webpage(url):
    """测试获取网页内容"""
    try:
        response = requests.get(url, headers=HEADERS, timeout=REQUEST_TIMEOUT, proxies={'http': None, 'https': None})
        response.raise_for_status()
        
        if response.encoding == 'ISO-8859-1':
            response.encoding = response.apparent_encoding or 'utf-8'
        elif not response.encoding:
            response.encoding = 'utf-8'
            
        soup = BeautifulSoup(response.text, 'html.parser')
        title = soup.find('title')
        page_title = title.get_text().strip() if title else "未获取到标题"
        
        print(f"✓ 网页获取成功，页面标题: {page_title}")
        return True, response.text
        
    except Exception as e:
        print(f"✗ 网页获取失败: {str(e)}")
        return False, ""

def test_extract_links(html_content):
    """测试提取新闻链接"""
    try:
        tree = html.fromstring(html_content)
        news_items = tree.xpath(NEWS_ITEM_XPATH)
        
        print(f"找到 {len(news_items)} 个新闻项")
        
        links = []
        for i, item in enumerate(news_items[:5]):  # 只测试前5个
            # 提取标题
            title_elements = item.xpath(NEWS_TITLE_XPATH)
            if not title_elements:
                print(f"  项目 {i+1}: 未找到标题链接")
                continue
                
            title_text = title_elements[0].text_content().strip() if hasattr(title_elements[0], 'text_content') else str(title_elements[0]).strip()
            title_link = title_elements[0].get('href')
            
            if not title_link:
                print(f"  项目 {i+1}: 标题链接为空")
                continue
                
            # 提取日期
            date_elements = item.xpath(NEWS_DATE_XPATH)
            if date_elements:
                date_text = date_elements[0].strip() if isinstance(date_elements[0], str) else str(date_elements[0]).strip()
            else:
                date_text = "未找到日期"
            
            # 处理相对URL
            if title_link.startswith('./'):
                current_page_base = "https://shgz.nanjing.gov.cn/gzdt/jcdt/"
                full_url = urljoin(current_page_base, title_link)
            elif title_link.startswith('../../'):
                full_url = urljoin(BASE_URL, title_link)
            else:
                full_url = urljoin(BASE_URL, title_link)
            
            links.append({
                'url': full_url,
                'title': title_text,
                'date': date_text
            })
            
            print(f"  项目 {i+1}: {title_text[:50]}{'...' if len(title_text) > 50 else ''}")
            print(f"    日期: {date_text}")
            print(f"    链接: {full_url}")
            
        return links
        
    except Exception as e:
        print(f"提取链接失败: {str(e)}")
        return []

def main():
    """主测试函数"""
    print("=" * 60)
    print("测试新配置的爬虫")
    print(f"目标URL: {START_URL}")
    print("=" * 60)
    
    # 测试获取网页
    print("\n1. 测试获取网页...")
    success, html_content = test_fetch_webpage(START_URL)
    if not success:
        print("网页获取失败，测试终止")
        return
    
    # 测试提取链接
    print("\n2. 测试提取新闻链接...")
    links = test_extract_links(html_content)
    
    if links:
        print(f"\n✓ 成功提取 {len(links)} 个新闻链接")
        
        # 测试访问第一个文章详情页
        print("\n3. 测试访问文章详情页...")
        first_link = links[0]
        success, article_html = test_fetch_webpage(first_link['url'])
        if success:
            print("✓ 文章详情页访问成功")
        else:
            print("✗ 文章详情页访问失败")
    else:
        print("✗ 未能提取到任何新闻链接")
    
    print("\n测试完成")

if __name__ == "__main__":
    main()
