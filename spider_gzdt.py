# -*- coding: utf-8 -*=
"""
南京市委组织部工作动态爬虫
适配新URL: https://zzb.nanjing.gov.cn/lgbgz/gzdt/index.html
专门爬取2025年5-7月工作动态内容
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
from lxml import html
import re
from datetime import datetime
import os
import sys
import time
from urllib.parse import urljoin, urlparse

try:
    from openpyxl.styles import Alignment
except ImportError:
    Alignment = None

from config_gzdt import (
    START_URL, BASE_URL, LIST_CONTAINER_XPATH, NEWS_ITEM_XPATH, 
    NEWS_TITLE_XPATH, NEWS_DATE_XPATH, NEXT_PAGE_XPATH,
    PAGE_URL_PATTERNS, FIRST_PAGE_URL, MAX_PAGES, START_PAGE,
    TARGET_DATE_RANGE, OUTPUT_FILENAME, HEADERS, REQUEST_TIMEOUT, 
    MAX_RETRIES, RETRY_DELAY, VERIFY_SSL, PROXIES, 
    CONTENT_XPATHS, TITLE_XPATHS, PUBLISH_TIME_XPATHS
)

class GzdtSpider:
    """南京市委组织部工作动态爬虫类"""
    
    def __init__(self):
        """初始化组织部爬虫"""
        self.start_url = START_URL
        self.base_url = BASE_URL
        
        # XPath选择器
        self.list_container_xpath = LIST_CONTAINER_XPATH
        self.news_item_xpath = NEWS_ITEM_XPATH
        self.news_title_xpath = NEWS_TITLE_XPATH
        self.news_date_xpath = NEWS_DATE_XPATH
        self.next_page_xpath = NEXT_PAGE_XPATH
        
        # 分页配置
        self.page_url_patterns = PAGE_URL_PATTERNS
        self.first_page_url = FIRST_PAGE_URL
        self.max_pages = MAX_PAGES
        self.start_page = START_PAGE
        self.current_pattern = None  # 动态确定有效的分页模式
        
        # 筛选和输出配置
        self.target_date_range = TARGET_DATE_RANGE
        self.output_file = OUTPUT_FILENAME
        
        # 网络配置
        self.headers = HEADERS
        self.timeout = REQUEST_TIMEOUT
        self.max_retries = MAX_RETRIES
        self.retry_delay = RETRY_DELAY
        self.verify_ssl = VERIFY_SSL
        self.proxies = PROXIES
        
        # 内容提取配置
        self.content_xpaths = CONTENT_XPATHS
        self.title_xpaths = TITLE_XPATHS
        self.publish_time_xpaths = PUBLISH_TIME_XPATHS
        
        # 统计信息
        self.processed_articles = 0
        self.filtered_articles = 0
        
    def fetch_webpage(self, url):
        """获取网页内容（带重试机制）"""
        for attempt in range(self.max_retries):
            try:
                if attempt > 0:
                    print(f"  第 {attempt + 1} 次重试...")
                    time.sleep(self.retry_delay)

                request_params = {
                    'headers': self.headers,
                    'timeout': self.timeout,
                    'verify': self.verify_ssl
                }

                if self.proxies:
                    request_params['proxies'] = self.proxies
                else:
                    request_params['proxies'] = {'http': None, 'https': None}

                response = requests.get(url, **request_params)
                response.raise_for_status()

                # 智能编码检测
                if response.encoding == 'ISO-8859-1':
                    response.encoding = response.apparent_encoding or 'utf-8'
                elif not response.encoding:
                    response.encoding = 'utf-8'

                # 获取页面标题用于调试
                soup = BeautifulSoup(response.text, 'html.parser')
                title = soup.find('title')
                page_title = title.get_text().strip() if title else "未获取到标题"

                print(f"  ✓ 页面获取成功: {page_title}")
                return True, response.text, page_title

            except requests.exceptions.Timeout:
                error_msg = f"请求超时 (超过{self.timeout}秒)"
                print(f"  ✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except requests.exceptions.ConnectionError as e:
                error_msg = f"网络连接错误: {str(e)}"
                print(f"  ✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, "网络连接错误，请检查网络", ""

            except requests.exceptions.HTTPError as e:
                error_msg = f"HTTP错误: {e}"
                print(f"  ✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except Exception as e:
                error_msg = f"获取页面失败: {str(e)}"
                print(f"  ✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

        return False, "所有重试失败", ""

    def extract_links_from_page(self, html_content, base_url):
        """从页面提取工作动态链接，支持日期筛选"""
        try:
            tree = html.fromstring(html_content)
            news_items = tree.xpath(self.news_item_xpath)
            
            if not news_items:
                print(f"  ⚠️  未找到任何新闻项，XPath可能需要调整: {self.news_item_xpath}")
                return []
            
            print(f"  📋 共找到 {len(news_items)} 条新闻")
            
            valid_links = []
            
            for idx, item in enumerate(news_items, 1):
                try:
                    # 提取标题
                    title_elements = item.xpath(self.news_title_xpath)
                    if not title_elements:
                        print(f"    跳过第{idx}条: 未找到标题")
                        continue
                        
                    title_text = title_elements[0].text_content().strip()
                    title_link = title_elements[0].get('href') if title_elements[0].get('href') else ''
                    
                    if not title_link:
                        # 可能是标题元素本身就是链接
                        link_elements = item.xpath('.//a[@href]')
                        if link_elements:
                            title_link = link_elements[0].get('href')
                            title_text = link_elements[0].text_content().strip()
                        else:
                            print(f"    跳过第{idx}条: 未找到链接")
                            continue
                    
                    # 提取日期（智能匹配多个位置）
                    date_match = None
                    date_text = ""
                    
                    # 方法1: 使用指定的日期XPath
                    date_elements = item.xpath(self.news_date_xpath)
                    if date_elements:
                        date_text = date_elements[0].text_content().strip()
                    
                    # 方法2: 在文本中寻找日期模式
                    if not self.is_valid_date_format(date_text):
                        item_text = item.text_content()
                        date_match = re.search(r'20(?:25)-?(?:0?[5-7]|5|6|7)', item_text)
                        if date_match:
                            date_text = date_match.group()
                    
                    if not date_text:
                        print(f"    跳过第{idx}条: 未找到日期")
                        continue
                    
                    # 检查是否属于目标日期范围（增强的匹配逻辑）
                    date_key = self.extract_date_key(date_text)
                    if not date_key or not self.is_in_target_range(date_key):
                        self.filtered_articles += 1
                        print(f"    跳过第{idx}条: 日期{date_text}不在5-7月范围内")
                        continue
                    
                    # 处理URL - 组织部工作动态专用路径处理
                    if not title_link.startswith('http'):
                        # 清理路径开头字符
                        clean_link = title_link.lstrip('./')
                        
                        # 根据路径格式选择正确的base URL
                        if clean_link.startswith('2025'):  # 如 202507/t20250707_5601799.html
                            current_page_base = "https://zzb.nanjing.gov.cn/lgbgz/gzdt/"
                            title_link = urljoin(current_page_base, clean_link)
                        elif clean_link.startswith('/lgbgz/gzdt/'):  # 绝对路径
                            title_link = urljoin("https://zzb.nanjing.gov.cn", clean_link)
                        else:
                            # 标准相对路径
                            title_link = urljoin("https://zzb.nanjing.gov.cn/lgbgz/gzdt/", clean_link)
                    
                    valid_links.append({
                        'url': title_link,
                        'title': title_text,
                        'date': date_text,
                        'date_key': date_key
                    })
                    
                except Exception as e:
                    print(f"    处理第{idx}条时出错: {str(e)}")
                    continue
            
            print(f"  ✅ 筛选后找到 {len(valid_links)} 条目标日期新闻")
            return valid_links
            
        except Exception as e:
            print(f"  ❌ 提取链接失败: {str(e)}")
            return []

    def is_valid_date_format(self, date_text):
        """验证日期格式是否有效"""
        if not date_text:
            return False
        date_patterns = [
            r'2025[年\-./]?0?[5-7][月\-./]?[\d\-]*',
            r'0?[5-7][月\-./][2025]*'
        ]
        return any(re.search(pattern, date_text) for pattern in date_patterns)

    def extract_date_key(self, date_text):
        """从日期文本中提取标准化日期键值"""
        if not date_text:
            return None
            
        # 统一清理格式
        date_text = re.sub(r'[年月日/\.年]', '-', date_text)
        
        # 提取2025-05或2025-5格式
        matches = re.findall(r'2025\-?0?([5-7])', date_text)
        if matches:
            month = int(matches[0])
            return f"2025-{month:02d}"
        
        return None

    def is_in_target_range(self, date_key):
        """检查日期是否在目标范围内"""
        return any(target in date_key for target in ['2025-05', '2025-06', '2025-07'])

    def extract_article_content(self, url):
        """提取文章内容（标题、正文、发布日期）"""
        print(f"  📄 正在解析文章: {url}")
        
        success, html_content, _ = self.fetch_webpage(url)
        if not success:
            print(f"    ❌ 无法获取文章页面: {html_content}")
            return "", "", ""

        try:
            tree = html.fromstring(html_content)
            
            # 提取标题
            title = ""
            for title_xpath in self.title_xpaths:
                title_elements = tree.xpath(title_xpath)
                if title_elements:
                    title = title_elements[0].text_content().strip()
                    if title and len(title) > 3:  # 合理长度验证
                        break
            
            # 提取正文内容
            content = ""
            for content_xpath in self.content_xpaths:
                content_elements = tree.xpath(content_xpath)
                if content_elements:
                    paragraphs = [elem.text_content().strip() for elem in content_elements if elem.text_content().strip()]
                    content = '\n\n'.join(paragraphs)
                    if len(content) > 50:  # 最小内容长度
                        break

            # 提取发布时间
            publish_time = ""
            for time_xpath in self.publish_time_xpaths:
                time_elements = tree.xpath(time_xpath)
                if time_elements:
                    if isinstance(time_elements[0], str):
                        publish_time = time_elements[0].strip()
                    else:
                        raw_time = time_elements[0].text_content().strip()
                        date_match = re.search(r'20(?:25)\D?(?:0?[1-9]|1[0-2])\D?(?:0?[1-9]|[12][0-9]|3[01])?', raw_time)
                        if date_match:
                            publish_time = date_match.group()
                        else:
                            publish_time = raw_time[:50]  # 截取合理长度
                    break

            return self.clean_text(title), self.clean_text(content), publish_time
            
        except Exception as e:
            print(f"    ❌ 解析文章内容失败: {str(e)}")
            return "", "", ""

    def clean_text(self, text):
        """高级文本清理"""
        if not text:
            return ""
        
        try:
            # HTML标签清理
            text = re.sub(r'<[^>]+>', '', text)
            
            # HTML实体转义
            entity_map = {
                '&nbsp;': ' ', '&lt;': '<', '&gt;': '>', 
                '&amp;': '&', '&quot;': '"', '&#39;': "'",
                '&hellip;': '...', '&mdash;': '—', '&ndash;': '–'
            }
            
            for entity, char in entity_map.items():
                text = text.replace(entity, char)
            
            # 空白字符标准化
            text = re.sub(r'\s+', ' ', text)
            text = re.sub(r'[\r\n]+', '\n\n', text)
            
            return text.strip()
            
        except Exception:
            return str(text).strip()

    def construct_next_url(self, current_page):
        """构造下一页URL（动态选择有效模式）"""
        if not self.current_pattern and current_page <= 2:
            # 前2页用来探测有效的分页模式
            for pattern in self.page_url_patterns:
                test_url = pattern.format(current_page)
                print(f"    📋 测试分页模式: {test_url}")
                success, _, _ = self.fetch_webpage(test_url)
                if success:
                    self.current_pattern = pattern
                    print(f"    ✅ 确定分页模式: {pattern}")
                    return test_url
            
            # 找不到有效模式时使用第一页
            print("    ⚠️  找不到有效分页模式，仅爬取第一页")
            return None
            
        elif self.current_pattern:
            return self.current_pattern.format(current_page)
        
        return None

    def crawl_all_pages(self):
        """主爬取流程"""
        print("🚀 开始爬取南京市委组织部工作动态...")
        print(f"📝 目标日期: 2025年5-7月")
        print(f"📂 输出文件: {self.output_file}")
        print("=" * 60)

        all_articles = []
        
        # 创建输出目录
        output_dir = os.path.dirname(self.output_file) or "spiderdata"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        page = self.start_page
        
        while page <= self.max_pages:
            current_url = self.construct_next_url(page) if page > 1 else self.first_page_url
            if not current_url:
                break
                
            print(f"📄 正在处理第 {page} 页: {current_url}")
            
            success, html_content, page_title = self.fetch_webpage(current_url)
            if not success:
                print(f"  ❌ 获取页面失败，跳过: {html_content}")
                page += 1
                continue

            # 提取新闻链接
            articles = self.extract_links_from_page(html_content, self.base_url)
            if not articles:
                print("  ⚠️  本页无目标新闻，可能已到最后一页")
                break

            # 逐条处理文章
            for article in articles:
                title, content, publish_time = self.extract_article_content(article['url'])
                
                if title and content:
                    all_articles.append({
                        '标题': title,
                        '发布日期': publish_time or article['date'],
                        '原日期': article['date'],
                        '链接': article['url'],
                        '正文': content,
                        '字数': len(content)
                    })
                    
                    self.processed_articles += 1
                    print(f"    ✅ 已处理: {title[:30]}...")
                    
                    # 进度显示
                    if self.processed_articles % 5 == 0:
                        print(f"📝 进度: 已处理{self.processed_articles}篇文章")
                
                # 防止请求过快
                time.sleep(0.5)

            page += 1

 

        # 修复：无论是否有数据都保存
        print(f"🔍 准备保存 {len(all_articles)} 条记录到Excel...")
        
        # 确保输出目录存在
        output_dir = "spiderdata"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 强制保存结果
        try:
            if all_articles:
                df = pd.DataFrame(all_articles)
                full_path = os.path.abspath(self.output_file)
                
                print(f"💾 正在保存到：{full_path}")
                with pd.ExcelWriter(self.output_file, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='组织部工作动态', index=False)
                    
                if os.path.exists(self.output_file):
                    file_size = os.path.getsize(self.output_file)
                    print(f"✅ 保存成功！文件大小：{file_size} 字节")
                    print(f"📁 完整路径：{full_path}")
            else:
                print("⚠️ 无数据，但创建空文件")
                df = pd.DataFrame(columns=['标题', '发布日期', '链接', '正文'])
                with pd.ExcelWriter(self.output_file, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='组织部工作动态', index=False)
            
        except Exception as e:
            print(f"❌ Excel保存失败：{str(e)}")
            # CSV备份
            try:
                csv_file = self.output_file.replace('.xlsx', '.csv')
                pd.DataFrame(all_articles).to_csv(csv_file, encoding='utf-8-sig', index=False)
                print(f"✅ CSV备份：{csv_file}")
            except Exception as csv_error:
                print(f"🚨 所有保存都失败：{csv_error}")

        return all_articles

if __name__ == '__main__':
    spider = GzdtSpider()
    articles = spider.crawl_all_pages()
    print(f"🎉 爬取完成！共{len(articles or [])}条记录已保存")