# -*- coding: utf-8 -*-
"""
南京市纪委监委新闻分页爬虫
专门用于爬取要闻页面的分页内容并输出为Excel格式
支持按日期范围筛选新闻（5-7月）
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
from lxml import html
import re
from datetime import datetime
import os
import sys
import time
from urllib.parse import urljoin
try:
    from openpyxl.styles import Alignment
except ImportError:
    Alignment = None

from config_news import (
    START_URL, BASE_URL, LIST_CONTAINER_XPATH, NEWS_ITEM_XPATH, NEWS_TITLE_XPATH, NEWS_DATE_XPATH, NEXT_PAGE_XPATH,
    PAGE_URL_PATTERN, FIRST_PAGE_URL, MAX_PAGES, START_PAGE,
    TARGET_DATE_RANGE, OUTPUT_FILENAME, HEADERS, REQUEST_TIMEOUT, MAX_RETRIES, RETRY_DELAY,
    VERIFY_SSL, PROXIES, CONTENT_XPATHS, TITLE_XPATHS, PUBLISH_TIME_XPATHS
)


class NewsSpider:
    """南京欧美同学会新闻分页爬虫类"""

    def __init__(self):
        """初始化爬虫"""
        self.start_url = START_URL
        self.base_url = BASE_URL
        self.list_container_xpath = LIST_CONTAINER_XPATH
        self.news_item_xpath = NEWS_ITEM_XPATH
        self.news_title_xpath = NEWS_TITLE_XPATH
        self.news_date_xpath = NEWS_DATE_XPATH
        self.next_page_xpath = NEXT_PAGE_XPATH
        self.page_url_pattern = PAGE_URL_PATTERN
        self.first_page_url = FIRST_PAGE_URL
        self.max_pages = MAX_PAGES
        self.start_page = START_PAGE
        self.target_date_range = TARGET_DATE_RANGE
        self.headers = HEADERS
        self.timeout = REQUEST_TIMEOUT
        self.output_file = OUTPUT_FILENAME
        self.max_retries = MAX_RETRIES
        self.retry_delay = RETRY_DELAY
        self.verify_ssl = VERIFY_SSL
        self.proxies = PROXIES
        self.content_xpaths = CONTENT_XPATHS
        self.title_xpaths = TITLE_XPATHS
        self.publish_time_xpaths = PUBLISH_TIME_XPATHS
        
    def fetch_webpage(self, url):
        """
        获取网页内容（带重试机制）
        Args:
            url: 目标网址
        Returns:
            tuple: (是否成功, 网页内容/错误信息, 页面标题)
        """
        for attempt in range(self.max_retries):
            try:
                if attempt > 0:
                    print(f"第 {attempt + 1} 次尝试...")
                    time.sleep(self.retry_delay)

                # 构建请求参数
                request_params = {
                    'headers': self.headers,
                    'timeout': self.timeout,
                    'verify': self.verify_ssl
                }

                # 代理设置
                if self.proxies:
                    request_params['proxies'] = self.proxies
                else:
                    request_params['proxies'] = {'http': None, 'https': None}

                response = requests.get(url, **request_params)
                response.raise_for_status()

                # 编码检测
                if response.encoding == 'ISO-8859-1':
                    response.encoding = response.apparent_encoding or 'utf-8'
                elif not response.encoding:
                    response.encoding = 'utf-8'

                # 获取页面标题
                soup = BeautifulSoup(response.text, 'html.parser')
                title = soup.find('title')
                page_title = title.get_text().strip() if title else "未获取到标题"

                print(f"✓ 网页获取成功，页面标题: {page_title}")
                return True, response.text, page_title

            except requests.exceptions.Timeout:
                error_msg = f"请求超时 (超过{self.timeout}秒)"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except requests.exceptions.ConnectionError as e:
                error_msg = f"网络连接错误: {str(e)}"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, "网络连接错误，请检查网络连接或防火墙设置", ""

            except requests.exceptions.HTTPError as e:
                error_msg = f"HTTP错误: {e}"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except Exception as e:
                error_msg = f"获取网页时发生未知错误: {str(e)}"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

        return False, "所有重试都失败了", ""

    def extract_links_from_xpath(self, html_content, _unused_xpath, base_url):
        """
        从指定XPath提取所有链接和目标日期范围的新闻
        Args:
            html_content: HTML内容
            xpath: XPath路径
            base_url: 基础URL
        Returns:
            list: 链接列表
        """
        try:
            tree = html.fromstring(html_content)
            # 查找新闻列表中的每项
            news_items = tree.xpath(self.news_item_xpath)

            links = []
            for item in news_items:
                # 提取标题
                title_elements = item.xpath(self.news_title_xpath)
                if not title_elements:
                    continue
                    
                title_text = title_elements[0].text_content().strip() if hasattr(title_elements[0], 'text_content') else str(title_elements[0]).strip()
                title_link = title_elements[0].get('href')
                
                if not title_link:
                    continue
                    
                # 提取日期并筛选
                date_elements = item.xpath(self.news_date_xpath)
                if not date_elements:
                    continue
                    
                date_text = date_elements[0].text_content().strip() if hasattr(date_elements[0], 'text_content') else str(date_elements[0]).strip()
                
                # 检查是否属于目标日期范围
                target_found = False
                for target_date in self.target_date_range:
                    if target_date in date_text:
                        target_found = True
                        break
                        
                if not target_found:
                    continue
                
                # 处理相对URL - 修复URL拼接问题
                if title_link.startswith('./'):
                    # 相对路径，需要基于当前页面路径拼接
                    current_page_base = "https://jw.nanjing.gov.cn/xwzx/yw/"
                    full_url = urljoin(current_page_base, title_link)
                else:
                    full_url = urljoin(base_url, title_link)
                
                links.append({
                    'url': full_url,
                    'title': title_text,
                    'date': date_text
                })

            print(f"  找到 {len(links)} 条目标日期范围的新闻")
            return links

        except Exception as e:
            print(f"  提取链接失败: {str(e)}")
            return []

    def extract_article_content(self, url):
        """
        提取文章详情页的标题、正文和发表时间
        Args:
            url: 文章URL
        Returns:
            tuple: (标题, 正文内容, 发表时间)
        """
        print(f"  正在提取文章内容: {url}")

        # 获取文章页面
        success, html_content, _ = self.fetch_webpage(url)
        if not success:
            print(f"    获取文章页面失败: {html_content}")
            return "", "", ""

        try:
            tree = html.fromstring(html_content)

            # 提取标题
            title = ""
            for title_xpath in self.title_xpaths:
                title_elements = tree.xpath(title_xpath)
                if title_elements:
                    title = title_elements[0].text_content().strip()
                    if title:
                        break

            # 提取正文内容
            content = ""
            for content_xpath in self.content_xpaths:
                content_elements = tree.xpath(content_xpath)
                if content_elements:
                    content_parts = []
                    for element in content_elements:
                        if hasattr(element, 'text_content'):
                            text = element.text_content()
                            content_parts.append(text)
                    content = '\n'.join(content_parts)
                    if content.strip():
                        break

            # 提取发表时间
            publish_time = ""
            for time_xpath in self.publish_time_xpaths:
                time_elements = tree.xpath(time_xpath)
                if time_elements:
                    if isinstance(time_elements[0], str):
                        publish_time = time_elements[0].strip()
                    elif hasattr(time_elements[0], 'text_content'):
                        publish_time = time_elements[0].text_content().strip()
                    elif hasattr(time_elements[0], 'text'):
                        publish_time = time_elements[0].text.strip() if time_elements[0].text else ""
                    else:
                        publish_time = str(time_elements[0]).strip()

                    # 清理发表时间，提取日期部分
                    if publish_time and '2025' in publish_time:
                        # 使用正则表达式提取日期
                        import re
                        date_match = re.search(r'2025-\d{2}-\d{2}', publish_time)
                        if date_match:
                            publish_time = date_match.group()

                    if publish_time:
                        break

            # 清理文本
            content = self.clean_text(content)

            print(f"    提取成功 - 标题: {title[:50]}{'...' if len(title) > 50 else ''}")
            print(f"    内容长度: {len(content)} 字符")
            print(f"    发表时间: {publish_time}")

            return title, content, publish_time

        except Exception as e:
            print(f"    提取文章内容失败: {str(e)}")
            return "", "", ""

    def clean_text(self, text):
        """清理文本内容"""
        if not text:
            return ""
        try:
            # 移除HTML标签和实体
            text = re.sub(r'<[^>]+>', '', text)
            text = text.replace('&nbsp;', ' ').replace('&lt;', '<').replace('&gt;', '>')
            text = text.replace('&amp;', '&').replace('&quot;', '"').replace('&#39;', "'")

            # 清理空白字符
            text = re.sub(r'\s+', ' ', text)
            text = re.sub(r'\n\s*\n', '\n\n', text)
            return text.strip()
        except:
            return text

    def find_next_page_url(self, _current_url, current_page_num):
        """
        根据当前页码构造下一页URL
        Args:
            current_url: 当前页面URL
            current_page_num: 当前页码
        Returns:
            str: 下一页URL，如果没有则返回None
        """
        try:
            next_page_num = current_page_num + 1

            # 根据页码构造下一页URL
            if next_page_num == 1:
                # 第二页使用 index_1.html 格式
                next_url = self.page_url_pattern.format(1)
            else:
                # 其他页面使用 index_N.html 格式
                next_url = self.page_url_pattern.format(next_page_num)

            print(f"  构造下一页URL: {next_url}")
            return next_url

        except Exception as e:
            print(f"  构造下一页URL失败: {str(e)}")
            return None

    def crawl_page_links(self, page_url):
        """
        爬取单页的所有文章链接和内容
        Args:
            page_url: 页面URL
        Returns:
            list: 文章数据列表
        """
        print(f"\n正在爬取页面: {page_url}")

        # 获取列表页面
        success, html_content, _ = self.fetch_webpage(page_url)
        if not success:
            print(f"获取页面失败: {html_content}")
            return []

        # 提取页面中目标日期范围的文章链接
        links = self.extract_links_from_xpath(html_content, "", self.base_url)
        if not links:
            print("未找到任何文章链接")
            return []

        print(f"找到 {len(links)} 个文章链接，开始提取内容...")

        articles = []
        for i, link in enumerate(links, 1):
            print(f"\n[{i}/{len(links)}] 处理文章: {link['title']}")

            # 提取文章内容
            title, content, publish_time = self.extract_article_content(link['url'])

            if title and content:
                articles.append({
                    'title': title,
                    'content': content,
                    'publish_time': publish_time,
                    'url': link['url'],
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
                print(f"    ✓ 成功提取")
            else:
                print(f"    ✗ 提取失败")

            # 添加延迟避免请求过快
            time.sleep(1)

        print(f"\n页面爬取完成，成功提取 {len(articles)} 篇文章")
        return articles

    def save_to_excel(self, all_articles):
        """
        保存所有结果到Excel文件（三列格式：标题、正文内容、发表时间）
        Args:
            all_articles: 所有文章数据列表
        Returns:
            bool: 是否保存成功
        """
        try:
            # 检查文件是否被占用
            if os.path.exists(self.output_file):
                try:
                    temp_name = self.output_file + '.tmp'
                    os.rename(self.output_file, temp_name)
                    os.rename(temp_name, self.output_file)
                except OSError:
                    print(f"文件被占用，请关闭Excel文件: {self.output_file}")
                    return False

            # 准备数据
            data = {
                '标题': [article['title'] for article in all_articles],
                '正文内容': [article['content'] for article in all_articles],
                '发表时间': [article['publish_time'] for article in all_articles]
            }

            df = pd.DataFrame(data)

            # 保存到Excel
            with pd.ExcelWriter(self.output_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='本会动态', index=False)

                # 调整列宽
                worksheet = writer.sheets['本会动态']
                worksheet.column_dimensions['A'].width = 50  # 标题列
                worksheet.column_dimensions['B'].width = 100  # 内容列
                worksheet.column_dimensions['C'].width = 20  # 发表时间列

                # 设置自动换行
                if Alignment:
                    for row in worksheet.iter_rows(min_row=2, max_row=worksheet.max_row):
                        for cell in row:
                            cell.alignment = Alignment(wrap_text=True, vertical='top')

            print(f"✓ 数据已保存到: {self.output_file}")
            return True

        except Exception as e:
            print(f"保存Excel失败: {str(e)}")
            return False

    def run_paginated_crawl(self):
        """
        执行分页爬取主逻辑
        Returns:
            bool: 是否成功完成爬取
        """
        print("南京市纪委监委新闻分页爬虫启动")
        print(f"起始URL: {self.start_url}")
        print(f"计划爬取页数: {self.max_pages}")
        print(f"目标日期范围: {', '.join(self.target_date_range)}")

        all_articles = []
        pages_crawled = 0

        for page_num in range(self.max_pages):
            pages_crawled = page_num + 1

            # 构造当前页面URL
            if page_num == 0:
                current_url = self.first_page_url  # 第一页使用 index.html
            else:
                current_url = self.page_url_pattern.format(page_num)  # 其他页面使用 index_N.html

            print(f"\n{'='*60}")
            print(f"正在爬取第 {page_num + 1}/{self.max_pages} 页")
            print(f"页面URL: {current_url}")
            print(f"{'='*60}")

            # 爬取当前页面的所有文章
            articles = self.crawl_page_links(current_url)
            if articles:
                all_articles.extend(articles)
                print(f"第 {page_num + 1} 页成功提取 {len(articles)} 篇文章")

                # 检查是否已经获取到足够早期的新闻（如果连续几页都没有目标日期的新闻，可以提前结束）
                has_target_date = any(any(target_date in article.get('publish_time', '') for target_date in self.target_date_range) for article in articles)
                if not has_target_date and page_num > 5:  # 如果超过5页还没有目标日期的新闻，可能已经爬取完毕
                    print(f"第 {page_num + 1} 页未找到目标日期范围的新闻，可能已爬取完毕")
            else:
                print(f"第 {page_num + 1} 页未提取到任何文章")
                # 如果连续几页都没有文章，可能已经到达最后一页
                if page_num > 2:
                    print("连续多页无文章，可能已到达最后一页，结束爬取")
                    break

            # 添加页面间延迟
            if page_num < self.max_pages - 1:
                print("等待2秒后继续...")
                time.sleep(2)

        # 保存结果
        print(f"\n{'='*60}")
        print("爬取完成，开始保存数据...")
        print(f"总共爬取了 {pages_crawled} 页，提取了 {len(all_articles)} 篇文章")

        if all_articles:
            if self.save_to_excel(all_articles):
                print(f"✓ 爬取任务完成！数据已保存到: {self.output_file}")
                return True
            else:
                print("✗ 数据保存失败")
                return False
        else:
            print("✗ 未提取到任何文章数据")
            return False


def main():
    """主函数"""
    print("=" * 80)
    print("南京市纪委监委新闻爬虫")
    print("目标网站: https://jw.nanjing.gov.cn/xwzx/yw/")
    print("爬取范围: 2025年5-7月要闻")
    print("=" * 80)
    
    try:
        # 创建爬虫实例
        spider = NewsSpider()
        
        # 显示配置信息
        print(f"\n配置信息:")
        print(f"起始URL: {spider.start_url}")
        print(f"最大页数: {spider.max_pages}")
        print(f"目标日期: {', '.join(spider.target_date_range)}")
        print(f"输出文件: {spider.output_file}")
        
        # 确认开始爬取
        user_input = input("\n是否开始爬取？(y/n): ").strip().lower()
        if user_input not in ['y', 'yes', '是']:
            print("用户取消操作")
            return
        
        # 开始爬取
        print("\n开始爬取...")
        success = spider.run_paginated_crawl()
        
        if success:
            print(f"\n✓ 爬取成功完成！")
            print(f"数据已保存到: {spider.output_file}")
        else:
            print(f"\n✗ 爬取失败")
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
